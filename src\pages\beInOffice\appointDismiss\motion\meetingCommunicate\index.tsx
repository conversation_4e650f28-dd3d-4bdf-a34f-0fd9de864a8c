import { Button, Input, Space, Select, Modal, message, Upload, Form } from 'antd';
import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import styles from '../consideration/index.less';
import TimePicker from '@/components/TimePicker';
import { Head, Colgroup, fakeLine } from '@/components/DynamicTableHead/index2';
import { isEmpty } from 'lodash';
import moment from 'moment';
import PersonModalSelect from '../consideration/components/PersonModalSelect';
import request from '@/utils/request';
import PositionSelectModal from '../consideration/components/PositionSelect';
import { _history } from '@/utils/session';
import { fileDownload } from '@/utils/method';
import { exportPlan } from '../../services';

// 定义人员信息接口
interface PersonInfo {
  A0000?: string;
  A0101?: string;
  A0192?: string;
}

// 定义行项目接口
interface RowItem {
  id: string;
  code: string;
  type1: string;
  typeCode1: string;
  type2: string;
  typeCode2: string;
  position?: {
    value: string;
    label: string;
  };
  // 第一个人员
  selectedPositionCode: string;
  selectedPositionName: string;
  selectedPersonIntroduction?: string;
  // 第二个人员
  selectedPositionCode2?: string;
  selectedPositionName2?: string;
  selectedPersonIntroduction2?: string;
  // 第三个人员
  selectedPositionCode3?: string;
  selectedPositionName3?: string;
  selectedPersonIntroduction3?: string;
  remark: string;
  sort: number;
  extend1?: any;
  extend2?: any;
  extend3?: any;
}

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

// 生成唯一ID
const generateUniqueId = () => {
  return `id_${Date.now()}_${Math.random()
    .toString(36)
    .substr(2, 9)}`;
};

const index = () => {
  const [form] = Form.useForm();

  // 更新数据结构，添加唯一ID
  const [data, setData] = useState<RowItem[]>([
    {
      id: generateUniqueId(),
      code: '',
      type1: '',
      typeCode1: '',
      type2: '',
      typeCode2: '',
      position: undefined,
      selectedPositionCode: '',
      selectedPositionName: '',
      remark: '',
      sort: 0, // 添加sort字段用于排序
    },
  ]);
  const [currentTime, setCurrentTime] = useState(moment());
  const [positionModalVisible, setPositionModalVisible] = useState(false);
  const [gwVisible, setGwVisible] = useState(false);
  const [currentEditId, setCurrentEditId] = useState<string>();
  const [loading, setLoading] = useState(false);
  const [schemeId, setSchemeId] = useState<string>(); // 修改类型定义
  const [unitName, setUnitName] = useState(''); // 制表单位
  const [code, setCode] = useState<string>();
  // 用于人员选择的状态
  const [currentPersonValue, setCurrentPersonValue] = useState<PersonInfo[]>([]);

  // 市委副书记等领导的沟通情况
  const [leaderNotes, setLeaderNotes] = useState({
    swfsjsz: '', // 市委副书记市长
    swfsj: '', // 市委副书记
    sjwsj: '', // 市纪委书记
    swzzbz: '', // 市委组织部长
    swfsjFile: '', //市委副书记 文件
    sjwsjFile: '', //市纪委书记 文件
    swzzbzFile: '', //市委组织部长 文件
    swfsjszFile: '' //市委副书记、市长 文件
  });

  // PersonModalSelect的DOM引用
  const personSelectButtonRef = useRef<HTMLButtonElement>(null);

  const querys = _history.location.query || {};
  const [importLoading, setImportLoading] = useState(false);
  const [fileUrl, setFileUrl] = useState(querys?.fileUrl || ''); // 文件下载路径
  const [htmlContent, setHtmlContent]: any = useState(sessionStorage.getItem('archiveMaterialsFile') || ''); // 从sessionStorage获取html内容

  const [currentRowChooseType, setCurrentRowChooseType] = useState<string>('1');
  const [initData, setInitData] = useState<any>();
  const [currentRowData, setCurrentRowData] = useState<RowItem>({
    id: generateUniqueId(),
    code: '',
    type1: '',
    typeCode1: '',
    type2: '',
    typeCode2: '',
    position: undefined,
    selectedPositionCode: '',
    selectedPositionName: '',
    remark: '',
    sort: 0, // 添加sort字段用于排序
  });

  // 定义表格组件接口
  interface TableComponentProps {
    data: any[];
    isReadOnly?: boolean;
    tableWidth?: string | number;
  }

  const openGwModal = async (item) => {
    console.log("🚀 ~ openGwModal ~ item:", item)
    await setCurrentRowData(item)
    await setCurrentRowChooseType(item.chooseType)
    form.setFieldsValue({
      introduction1: item.chooseType == '1' ? item.position?.label : item.selectedPositionName,
      introduction2: item.chooseType == '1' ? item.jobExtend1['jobDescription'] : undefined,
      fileUrldisplay: item.jobExtend1 ? item.jobExtend1['fileUrl'] : undefined,
    })
    setGwVisible(true)
  };

  // 表格组件 - 复制自 consideration 页面
  const TableComponent = React.useCallback(
    ({ data, isReadOnly = false, tableWidth = '100%' }: TableComponentProps) => {
      return (
        <div style={isReadOnly ? { maxHeight: '550px', overflowY: 'auto', marginBottom: 30, width: '100%' } : {}}>
          <div className={styles.tableHeadBox}>
            <table className={styles.sftable} style={{ width: tableWidth, borderBottom: 'none' }}>
              <colgroup>{renderCols()}</colgroup>
              <Head bgColor="#e4e6e9" tree={tableHead} nodeName={'name'} nodeKey={'key'} />
            </table>
          </div>
          <div className={styles.tableHeadBox} style={isReadOnly ? {} : {}}>
            <table className={styles.sftable} style={{ width: tableWidth, borderTop: 'none' }}>
              <colgroup>{renderCols()}</colgroup>
              <tbody>
                {!isEmpty(data) &&
                  data.map((item: any, index: number) => {
                    const speciales = item.typeCode1 == '6' || item.typeCode1 == '5' || item.typeCode1 == '4';
                    return (
                      <tr className={styles.tableRow} key={item.id}>
                        {/* 只读模式 - 删除序号和操作列 */}
                        <>
                          <td className={speciales ? styles['dictCenter'] : ''} colSpan={speciales ? 2 : 1}>
                            <div style={{ textAlign: 'center', padding: '8px 4px' }}>{item.type1 || ''}</div>
                          </td>
                          <td style={{ display: speciales ? 'none' : 'table-cell' }}>
                            <div style={{ textAlign: 'center', padding: '8px 4px' }}>{item.type2 || ''}</div>
                          </td>
                          <td className={styles['person-select']} style={{ position: 'relative' }} onClick={() => item.chooseType == 1 && openGwModal(item)}>
                            {!speciales ? (
                              <div style={{ width: '100%', padding: 0, whiteSpace: 'pre-wrap', textAlign: 'center' }} >
                                <span style={{ whiteSpace: 'pre-wrap' }}>{item.position ? item.position.label : ''}</span>
                                {/* 岗位说明 */}
                                <br />
                                {item.chooseType == '1' && (
                                  <span style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all', display: 'inline-block', fontSize: 15, fontWeight: 100 }}>
                                    {item.jobExtend1?.jobDescription ? `（${item.jobExtend1?.jobDescription}）` : ''}
                                  </span>
                                )}
                                {/* 以人找岗的介绍信息 */}
                                {item.chooseType == '2' && item?.extend1?.introduction && (
                                  <p style={{ textAlign: 'left', wordBreak: 'break-all', margin: '4px 0', fontSize: 15 }}>{item.extend1.introduction}</p>
                                )}
                              </div>
                            ) : (
                              <div style={{ width: '100%', padding: 0, whiteSpace: 'pre-wrap', textAlign: 'center' }}>
                                <span style={{ whiteSpace: 'pre-wrap' }}>{item.position ? item.position.label : ''}</span>
                              </div>
                            )}
                          </td>
                          {(() => {
                            if (speciales) {
                              const candidateList = item?.CandidateSpecialList || [];
                              return (
                                <td colSpan={3}>
                                  {!isEmpty(candidateList) &&
                                    candidateList?.map?.((person, personIndex) => {
                                      return (
                                        <div key={personIndex} style={{ textIndent: '0', display: 'flex', justifyContent: 'start', alignItems: 'center', paddingLeft: 20, marginBottom: '4px' }}>
                                          <span style={{ whiteSpace: 'nowrap', width: 60 }}>{person.candidateName}</span>
                                          <span style={{ fontFamily: 'fzfs', fontSize: 16, marginLeft: '10px' }}>{person.introduction}</span>
                                        </div>
                                      );
                                    })}
                                </td>
                              );
                            } else {
                              // 会议沟通页面只显示一个人员
                              const person = item.persons && item.persons[0] ? item.persons[0] : {};
                              const hasData = person && person.A0101;
                              // debugger
                              return (
                                <td className={styles['person-select']} colSpan={3} onClick={() => item.chooseType == 2 && openGwModal(item)}>
                                  {hasData ? (
                                    <div style={{ padding: 0, whiteSpace: 'pre-wrap', textAlign: 'center' }}>
                                      <React.Fragment>
                                        {/* 以人找岗  */}
                                        {item.chooseType == '2' && item.selectedPositionName}
                                        {/* {item.chooseType == '2' && person.A0101} */}
                                        {/* 其他情况 */}
                                        {item.chooseType != '2' && person.A0101}
                                        {item.chooseType != '2' &&
                                          (() => {
                                            const introduction = person.extendData1?.introduction || '';
                                            if (!introduction) return null;

                                            // 使用正则表达式匹配括号内的内容，但保留"（兼）"在原位置
                                            const tempText = introduction.replace(/（兼）/g, '@@JIANSIGN@@');
                                            const parts = tempText
                                              .split(/(\（[^）]*\）)/g)
                                              .filter(Boolean)
                                              .map(part => part.replace(/@@JIANSIGN@@/g, '（兼）'));

                                            return (
                                              <div style={{ textAlign: 'left', wordBreak: 'break-all' }}>
                                                {parts.map((part, pIndex) => {
                                                  // 检查是否是括号内容且不是"（兼）"
                                                  const isSpecialBracket = part.startsWith('（') && part.endsWith('）') && !part.includes('（兼）');

                                                  return (
                                                    <p
                                                      key={pIndex}
                                                      style={{
                                                        fontFamily: isSpecialBracket ? 'fzfs' : '',
                                                        fontSize: isSpecialBracket ? '16px' : '',
                                                      }}
                                                    >
                                                      {part}
                                                    </p>
                                                  );
                                                })}
                                              </div>
                                            );
                                          })()}
                                      </React.Fragment>
                                    </div>
                                  ) : (
                                    <div></div>
                                  )}
                                </td>
                              );
                            }
                          })()}
                          <td>
                            <div style={{ textAlign: 'center', padding: '8px 4px', minHeight: '40px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>{item.remark || ''}</div>
                          </td>
                        </>
                      </tr>
                    );
                  })}
              </tbody>
            </table>
          </div>
        </div>
      );
    },
    [renderCols, tableHead]
  );

  // 计算不重复的人员总数
  const calculateTotalPersons = () => {
    // 创建一个Set用于存储不重复的人员ID
    const uniquePersonIds = new Set<string>();

    // 遍历所有行项目
    data.forEach(item => {
      // 只有当selectedPositionCode存在且不为空时才计入总数
      if (item.selectedPositionCode) {
        uniquePersonIds.add(item.selectedPositionCode);
      }
    });

    // 返回不重复的人员数量
    return uniquePersonIds.size;
  };

  useEffect(() => {
    // 设置当前时间
    setCurrentTime(moment());

    // 获取URL参数，如果存在code则加载数据
    const query = new URLSearchParams(window.location.search);
    const code = query.get('code');

    if (code) {
      setSchemeId(code);
      loadScheme(code);
    }
  }, []);

  // 加载方案数据
  const loadScheme = async (id: string) => {
    try {
      setLoading(true);
      const res = await request(`/api/swzzbappoint/prepareCadres/findPrepareCadres?planCode=${id}&type=2`);
      if (res && res.code === 0) {
        const { tableTime, tableUnit, list = [] } = res.data;

        // 将后端返回的数据转换为本地数据格式
        if (list && list.length > 0) {
          setInitData(list)
          const formattedData = list.map((item, index) => {
            // 确保每个项都有id
            const id = item.code || generateUniqueId();

            // 直接使用后端字段，避免转换
            return {
              id,
              ...item,
              position: {
                value: item.positionCode || '',
                label: item.positionName || '',
              },
              sort: item.sort !== undefined ? item.sort : index, // 如果没有sort，使用索引
            };
          });

          // 转换数据格式为 consideration 页面的格式
          const convertedData = formattedData.map((item, index) => {
            const speciales = item.typeCode1 == '6' || item.typeCode1 == '5' || item.typeCode1 == '4';

            if (speciales) {
              // 特殊类型：解析 selectedPositionName 中的 JSON 数据
              let candidateSpecialList = [];
              try {
                candidateSpecialList = JSON.parse(item.selectedPositionName || '[]');
              } catch (error) {
                candidateSpecialList = [];
              }

              return {
                ...item,
                CandidateSpecialList: candidateSpecialList || [],
                persons: [], // 特殊类型不使用 persons 数组
              };
            } else {
              // 普通类型：会议沟通页面只有一个人员
              const persons: any = [];
              // 只处理第一个人员
              if (item.selectedCandidateName) {
                // 查找匹配的候选人介绍信息
                const extendList = [item.extend1, item.extend2, item.extend3].filter(Boolean);
                const matchedExtend = extendList.find(extend => extend.candidateCode === item.selectedCandidateCode);
                const introduction = matchedExtend?.introduction || '';

                persons.push({
                  A0101: item.selectedCandidateName,
                  extendData1: {
                    introduction: introduction,
                  },
                });
              } else {
                persons.push({}); // 空位置
              }

              return {
                ...item,
                persons: persons,
                CandidateSpecialList: [], // 普通类型不使用特殊列表
              };
            }
          });

          setData(convertedData);
        } else {
          // 如果列表为空，设置一个默认行
          setData([
            {
              id: generateUniqueId(),
              code: '',
              type1: '',
              typeCode1: '',
              type2: '',
              typeCode2: '',
              position: undefined,
              selectedPositionCode: '',
              selectedPositionName: '',
              remark: '',
              sort: 0,
            },
          ]);
        }

        if (tableTime) {
          setCurrentTime(moment(tableTime));
        }
        setUnitName(tableUnit || '');

        // 加载领导沟通情况
        await loadLeaderNotes(id);
      } else {
        message.error('方案加载失败');
      }
    } catch (error) {
      message.error('加载失败，请重试');
      console.error('加载方案失败:', error);
    } finally {
      setLoading(false);
    }
  };
  // 加载领导沟通情况
  const loadLeaderNotes = async (planCode: string) => {
    try {
      const res = await request(`/api/swzzbappoint/communicate/findTopicCommunicate?planCode=${planCode}`);
      if (res && res.code === 0 && res.data) {
        setCode(res.data.code);
        setLeaderNotes({
          swfsjsz: res.data.swfsjsz || '',
          swfsj: res.data.swfsj || '',
          sjwsj: res.data.sjwsj || '',
          swzzbz: res.data.swzzbz || '',
          swfsjFile: res.data.swfsjFile || '', //市委副书记 文件
          sjwsjFile: res.data.sjwsjFile || '', //市纪委书记 文件
          swzzbzFile: res.data.swzzbzFile || '', //市委组织部长 文件
          swfsjszFile: res.data.swfsjszFile || '' //市委副书记、市长 文件
        });
      }
    } catch (error) {
      console.error('加载领导沟通情况失败:', error);
      // 即使加载失败也不影响表格显示，只记录错误
    }
  };

  // 以下是为了解决lint错误而保留的空函数
  const handleUnitNameChange = () => { };
  const handleLeaderNoteChange = (field: string, value: string) => {
    // 更新领导沟通情况
    setLeaderNotes(prev => ({
      ...prev,
      [field]: value,
    }));
  };
  const handlePositionSelect = () => { };
  const handlePersonSelectConfirm = () => { };

  // 处理导出方案
  const handleExportPlan = async () => {
    if (!schemeId) {
      message.warning('请先保存方案再导出');
      return;
    }

    try {
      setLoading(true);
      const res = await exportPlan({ planCode: schemeId, type: 2 });

      // 使用fileDownload函数下载文件
      fileDownload(`/api${res.data.url}`);

      message.success('导出成功');
    } catch (error) {
      console.error('导出方案失败:', error);
      message.error('导出失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 提交结果确认
  const handleConfirmResult = async () => {
    if (!schemeId) {
      message.warning('方案编号不存在，无法提交');
      return;
    }

    // 显示确认对话框
    Modal.confirm({
      title: '确认提交',
      content: '确定要提交领导沟通情况吗？提交后将无法修改。',
      okText: '确认提交',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true);
          const params = {
            planCode: schemeId,
            code: code,
            swfsjsz: leaderNotes.swfsjsz || '',
            swfsj: leaderNotes.swfsj || '',
            sjwsj: leaderNotes.sjwsj || '',
            swzzbz: leaderNotes.swzzbz || '',
            swfsjFile: leaderNotes.swfsjFile || '', //市委副书记 文件
            sjwsjFile: leaderNotes.sjwsjFile || '', //市纪委书记 文件
            swzzbzFile: leaderNotes.swzzbzFile || '', //市委组织部长 文件
            swfsjszFile: leaderNotes.swfsjszFile || '' //市委副书记、市长 文件
          };

          const res = await request('/api/swzzbappoint/communicate/addUpdateTopic', {
            method: 'POST',
            body: { data: params },
          });

          if (res && res.code === 0) {
            message.success('结果提交成功');
          } else {
            message.error(res?.message || '结果提交失败');
          }
        } catch (error) {
          console.error('提交结果失败:', error);
          message.error('提交失败，请重试');
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // 表格头部渲染函数
  const renderCols = useCallback(() => {
    return new Array(7).fill('').map((item, index) => {
      if (index < 2) {
        return <col key={index} style={{ width: 120 }} />;
      }
      if (index > 2 && index < 6) {
        return <col key={index} style={{ width: 200 }} />;
      }
      return <col key={index} style={{ width: 150 }} />;
    });
  }, []);

  const tableHead = [
    {
      key: '02',
      name: '类型',
      parent: '-1',
      colSpan: 2,
    },
    {
      key: '03',
      name: '岗位',
      parent: '-1',
    },
    {
      key: '04',
      name: '有关人选',
      parent: '-1',
      colSpan: 3,
    },
    {
      key: '05',
      name: '备注',
      parent: '-1',
    },
  ];

  const getWidth = useCallback(() => {
    return renderCols().reduce((all, it) => {
      return all + it?.props?.style?.width || 0;
    }, 30);
  }, [renderCols]);

  const WIDTH = useMemo(() => getWidth(), [getWidth]);

  const planName = _history.location.query?.planName;
  // 处理文件上传前的检查
  const beforeUpload = file => {
    // 检查文件大小
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('文件大小不能超过2MB!');
      return Upload.LIST_IGNORE;
    }

    setImportLoading(true);
    return true;
  };
  // 导入文件
  const importFile = async (file) => {
    console.log('导入文件:', file);
    if (file.url) {
      setFileUrl(file.url);
      try {
        const importRes = await request(`/api/swzzbappoint/examinationMaterials/uploadArchiveMaterials?id=${file?.id}&url=${file.url}`, {
          method: 'GET',
        });

        if (importRes && importRes.code === 0) {
          message.success('导入成功');
          setFileUrl(importRes.data?.fileUrl || '');
          // 导入成功后页面显示只导入的内容，不再显示原来的页面
          setHtmlContent(importRes.data?.archiveMaterialsFile || '');
          setFileUrl(importRes.data?.fileUrl || '');
          sessionStorage.setItem('archiveMaterialsFile', importRes.data?.archiveMaterialsFile || ''); // 保存html内容到sessionStorage
          // 刷新综合任免卷 页面
          localStorage.setItem('archiveMaterialsPageKey', `${+new Date()}`);
          // 更新路由上的fileUrl参数
          const query = { ..._history.location.query, fileUrl: importRes.data?.fileUrl || '' };
          _history.replace({
            pathname: _history.location.pathname,
            query,
          });
        } else {
          message.error(importRes.message || '导入失败');
        }
      } catch (error) {
        console.error('导入失败:', error);
        message.error('导入失败，请重试');
      } finally {
        setImportLoading(false);
      }
    }
  };
  // 处理文件上传变更
  const handleUploadChange = info => {
    if (info.file.status === 'done') {
      if (info.file.response && info.file.response.code === 0) {
        const file = info.file.response.data[0];

        // 调用导入接口
        importFile(file);
      } else {
        message.error(info.file.response?.message || '文件上传失败');
        setImportLoading(false);
      }
    } else if (info.file.status === 'error') {
      message.error('文件上传失败');
      setImportLoading(false);
    }
  };

  // 处理文件上传变更
  const handleUploadleaderNotesChange = async (info, text?) => {
    if (info.file.status === 'done') {
      if (info.file.response && info.file.response.code === 0) {
        const file = info.file.response.data[0];
        if (text) {
          await setLeaderNotes({
            ...leaderNotes,
            [text]: file?.url
          })
        } else {
          return file.url;
        }

      } else {
        message.error(info.file.response?.message || '文件上传失败');
        setImportLoading(false);
      }
    } else if (info.file.status === 'error') {
      message.error('文件上传失败');
      setImportLoading(false);
    }
  };

  // 导出文件
  const doDownload = (info) => {
    if (info) {
      console.log(info);
      // fileDownload(`/api${fileUrl}`);
    }
    if (fileUrl) {
      fileDownload(`/api${fileUrl}`);
    }
  };
  // 岗位弹窗关闭
  const handleOnCancel = async () => {
    await form.resetFields()
    setGwVisible(false)
  }
  // 岗位弹窗确认
  const handleSubmit = async () => {
    try {
      const rowData = initData.filter(item => item.code === currentRowData.code)[0]
      const resItem = {
        ...rowData,
        code: currentRowData.code,
        extendData1: currentRowData.extend1,
        extendData2: currentRowData.extend2,
        extendData3: currentRowData.extend3,
        jobExtend1: {
          ...rowData.jobExtend1
        }
      }
      const value = await form.validateFields()
      console.log('qfqfqefqfqwfqqfq', value);
      if (value.fileUrl) {
        const fileUrl = await handleUploadleaderNotesChange(value.fileUrl)
        resItem.jobExtend1['fileUrl'] = fileUrl
      }
      if (currentRowChooseType == '2') {
        resItem['selectedPositionName'] = value.introduction1
      } else {
        resItem['positionName'] = value.introduction1
        resItem.jobExtend1['jobDescription'] = value.introduction2
      }
      const res = await request('/api/swzzbappoint/prepareCadres/updateJobInfo', {
        method: 'POST',
        body: {
          data: {
            planCode: schemeId,
            confirmPrepareCadres: [resItem]
          }
        },
      });
      if (res && res.code === 0) {
        message.success('保存成功')
        if (schemeId) {
          loadScheme(schemeId)
        }
      }
    } catch (error) {

    }
    setGwVisible(false)
  }


  return (
    <div>
      <Modal
        title={"岗位修改"}
        visible={gwVisible}
        onCancel={handleOnCancel}
        footer={[
          <Button key="cancel" onClick={handleOnCancel}>
            取消
          </Button>,
          <Button key="submit" type="primary" loading={loading} onClick={handleSubmit}>
            确定
          </Button>,
        ]}
        width={700}
        maskClosable={false}
        destroyOnClose
      >
        {(() => {
          console.log(currentRowData);
          return <Form
            {...formItemLayout}
            className={styles.confirmInfoForm}
            form={form}
          >

            <Form.Item name="introduction1" label="岗位" rules={[{ required: true, message: '请输入岗位' }]}>
              <Input placeholder="请输入岗位" />
            </Form.Item>

            {currentRowChooseType != '2' && <Form.Item name="introduction2" label="岗位说明">
              <Input.TextArea placeholder="请输入岗位说明" rows={3} />
            </Form.Item>}
            <Form.Item name="fileUrl" label="文件上传">
              <Upload
                headers={{ Authorization: sessionStorage.getItem('authorization') || '' }}
                action="/api/base/upload"
                showUploadList={true}
                accept=".docx,.doc"
              >
                <Button loading={importLoading}>
                  导入文件
                </Button>
              </Upload>
            </Form.Item>
            <div style={{
              width: '100%',
              wordWrap: 'break-word',
              wordBreak: 'break-all',      // 允许在单词内换行
              whiteSpace: 'normal',
              overflowWrap: 'break-word',
              color: '#36d',
              cursor: 'pointer',
              marginLeft: "165px"
            }} onClick={(e) => {
              e.stopPropagation()
              fileDownload(`/api${form.getFieldValue('fileUrldisplay')}`, form.getFieldValue('fileUrldisplay')?.split('\\').pop())
            }
            }>{form.getFieldValue('fileUrldisplay') ? form.getFieldValue('fileUrldisplay').split('\\').pop() : ''}</div>
          </Form>
        })()}
      </Modal>
      <div style={{ fontWeight: 'bold' }}>{planName || '2025年01月XXXX建议调整方案'}</div>
      {querys?.comprehensive !== 'true' && (
        <Space>
          {/* <Button type="primary">预览</Button> */}
          <Button type="primary" onClick={handleConfirmResult}>
            保存
          </Button>
          <Button type="primary" onClick={handleExportPlan}>
            导出
          </Button>
        </Space>
      )}
      {querys?.comprehensive === 'true' && (
        <Space>
          <Upload
            headers={{ Authorization: sessionStorage.getItem('authorization') || '' }}
            action="/api/base/upload"
            beforeUpload={beforeUpload}
            onChange={handleUploadChange}
            showUploadList={false}
            accept=".docx,.doc"
          >
            <Button type="primary" loading={importLoading}>
              导入文件
            </Button>
          </Upload>
          <Button type="primary" onClick={fileUrl ? doDownload : handleExportPlan}>
            导出文件
          </Button>
        </Space>
      )}
      {htmlContent ? (
        <div dangerouslySetInnerHTML={{ __html: htmlContent }} />
      ) : (
        <div style={{ width: WIDTH, margin: '0 auto' }}>
          <div className={styles.tableTitle}>
            提交酝酿的干部
            <div className={styles.desc}>（涉及{calculateTotalPersons()}人）</div>
          </div>
          <div className={styles.tableInfo}>
            <span>
              制表时间：
              <div style={{ display: 'inline-block' }}>
                <TimePicker regNowDate={false} value={currentTime} onChange={value => setCurrentTime(value)} format="YYYY.MM.DD" disabled={true}></TimePicker>
              </div>
            </span>
            <span>
              制表单位：
              <div style={{ display: 'inline-block' }}>
                <Input value={unitName} onChange={handleUnitNameChange} disabled={true}></Input>
              </div>
            </span>
          </div>
          {/* 使用与 consideration 页面相同的 TableComponent */}
          <TableComponent data={data} isReadOnly={true} tableWidth={WIDTH} />

          <div style={{ marginTop: '30px', marginBottom: '15px', fontWeight: 'bold', fontSize: '16px' }}>
            书记专题会其他成员沟通酝酿情况：
            <span style={{ color: '#999', fontSize: '14px', fontWeight: 'normal', marginLeft: '8px' }}>(请在下方输入各领导的沟通情况，输入完成后点击"保存"按钮提交)</span>
          </div>
          <div style={{ marginTop: '15px', fontWeight: 'bold' }}>市委副书记、市长：</div>
          <div>
            <Input.TextArea rows={3} value={leaderNotes.swfsjsz} onChange={e => handleLeaderNoteChange('swfsjsz', e.target.value)} placeholder="请输入市委副书记、市长的意见" />
            <Space style={{ marginTop: '10px' }}>
              <Upload
                headers={{ Authorization: sessionStorage.getItem('authorization') || '' }}
                action="/api/base/upload"
                onChange={(info) => handleUploadleaderNotesChange(info, 'swfsjFile')}
                showUploadList={false}
                accept=".docx,.doc"
              >
                <Button loading={importLoading}>
                  导入文件
                </Button>
              </Upload>
            </Space>
            <div style={{
              width: '100%',
              wordWrap: 'break-word',
              wordBreak: 'break-all',      // 允许在单词内换行
              whiteSpace: 'normal',
              overflowWrap: 'break-word',
              color: '#36d',
              cursor: 'pointer'
            }} onClick={(e) => {
              e.stopPropagation()
              fileDownload(`/api${leaderNotes?.swfsjFile}`, leaderNotes?.swfsjFile?.split('\\').pop())
            }
            }>{leaderNotes?.swfsjFile ? leaderNotes.swfsjFile.split('\\').pop() : ''}</div>
          </div>
          <div style={{ marginTop: '15px', fontWeight: 'bold' }}>市委副书记：</div>
          <div>
            <Input.TextArea rows={3} value={leaderNotes.swfsj} onChange={e => handleLeaderNoteChange('swfsj', e.target.value)} placeholder="请输入市委副书记的意见" />
            <Space style={{ marginTop: '10px' }}>
              <Upload
                headers={{ Authorization: sessionStorage.getItem('authorization') || '' }}
                action="/api/base/upload"
                onChange={(info) => handleUploadleaderNotesChange(info, 'sjwsjFile')}
                showUploadList={false}
                accept=".docx,.doc"
              >
                <Button loading={importLoading}>
                  导入文件
                </Button>
              </Upload>
            </Space>
            <div style={{
              width: '100%',
              wordWrap: 'break-word',
              wordBreak: 'break-all',      // 允许在单词内换行
              whiteSpace: 'normal',
              overflowWrap: 'break-word',
              color: '#36d',
              cursor: 'pointer'
            }} onClick={(e) => {
              e.stopPropagation()
              fileDownload(`/api${leaderNotes?.sjwsjFile}`, leaderNotes?.sjwsjFile?.split('\\').pop())
            }
            }>{leaderNotes?.sjwsjFile ? leaderNotes.sjwsjFile.split('\\').pop() : ''}</div>
          </div>
          <div style={{ marginTop: '15px', fontWeight: 'bold' }}>市纪委书记：</div>
          <div>
            <Input.TextArea rows={3} value={leaderNotes.sjwsj} onChange={e => handleLeaderNoteChange('sjwsj', e.target.value)} placeholder="请输入市纪委书记的意见" />
            <Space style={{ marginTop: '10px' }}>
              <Upload
                headers={{ Authorization: sessionStorage.getItem('authorization') || '' }}
                action="/api/base/upload"
                onChange={(info) => handleUploadleaderNotesChange(info, 'swzzbzFile')}
                showUploadList={false}
                accept=".docx,.doc"
              >
                <Button loading={importLoading}>
                  导入文件
                </Button>
              </Upload>
            </Space>
            <div style={{
              width: '100%',
              wordWrap: 'break-word',
              wordBreak: 'break-all',      // 允许在单词内换行
              whiteSpace: 'normal',
              overflowWrap: 'break-word',
              color: '#36d',
              cursor: 'pointer'
            }} onClick={(e) => {
              e.stopPropagation()
              fileDownload(`/api${leaderNotes?.swzzbzFile}`, leaderNotes?.swzzbzFile?.split('\\').pop())
            }
            }>{leaderNotes?.swzzbzFile ? leaderNotes.swzzbzFile.split('\\').pop() : ''}</div>
          </div>
          <div style={{ marginTop: '15px', fontWeight: 'bold' }}>市委组织部长：</div>
          <div>
            <Input.TextArea rows={3} value={leaderNotes.swzzbz} onChange={e => handleLeaderNoteChange('swzzbz', e.target.value)} placeholder="请输入市委组织部长的意见" />
            <Space style={{ marginTop: '10px' }}>
              <Upload
                headers={{ Authorization: sessionStorage.getItem('authorization') || '' }}
                action="/api/base/upload"
                onChange={(info) => handleUploadleaderNotesChange(info, 'swfsjszFile')}
                showUploadList={false}
                accept=".docx,.doc"
              >
                <Button loading={importLoading}>
                  导入文件
                </Button>
              </Upload>
            </Space>
            <div style={{
              width: '100%',
              wordWrap: 'break-word',
              wordBreak: 'break-all',      // 允许在单词内换行
              whiteSpace: 'normal',
              overflowWrap: 'break-word',
              color: '#36d',
              cursor: 'pointer'
            }} onClick={(e) => {
              e.stopPropagation()
              fileDownload(`/api${leaderNotes?.swfsjszFile}`, leaderNotes?.swfsjszFile?.split('\\').pop())
            }
            }>{leaderNotes?.swfsjszFile ? leaderNotes.swfsjszFile.split('\\').pop() : ''}</div>
          </div>
        </div>
      )}

      {/* 岗位选择弹窗 */}
      <PositionSelectModal visible={positionModalVisible} onCancel={() => setPositionModalVisible(false)} onSelect={handlePositionSelect} />

      {/* 人员选择组件，使用按钮引用 */}
      <div style={{ display: 'none' }}>
        <PersonModalSelect onChange={handlePersonSelectConfirm} value={currentPersonValue} maxCount={1} title={'选择人员'}>
          <Button ref={personSelectButtonRef} type="link">
            隐藏按钮
          </Button>
        </PersonModalSelect>
      </div>
    </div>
  );
};

export default index;
