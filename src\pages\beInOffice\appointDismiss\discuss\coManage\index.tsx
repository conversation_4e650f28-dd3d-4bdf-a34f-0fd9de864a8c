import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Space, Button, message, Spin, Upload } from 'antd';
import { _history } from '@/utils/session';
import styles from './index.less';
import request from '@/utils/request';
import { fileDownload } from '@/utils/method';
import EditWord from '@/components/EditWord';
import qs from 'qs';
// 定义表格项接口
interface TableItem {
  id: string;
  unit: string; // 单位
  position: string; // 岗位
  remarks: string; // 备注
  selectedCandidateName: string; // 有关人选姓名
  selectedCandidateCode: string; // 有关人选编码
  selectedPositionName: string; // 添加缺少的属性
  selectedPositionCode: string; // 添加缺少的属性
}

const MeetingCommunicate: React.FC = () => {
  // 状态管理
  const [tableData, setTableData] = useState<TableItem[]>([]);
  const [planCode, setPlanCode] = useState<string>('');
  const [planName, setPlanName] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [currentPerson, setCurrentPerson] = useState<TableItem | null>(null);

  const [saveBtnLoading, setSaveBtnLoading] = useState<boolean>(false);
  const [reportHtmlContent, setReportHtmlContent] = useState<string>(''); // 存储从接口获取的HTML内容
  const [importLoading, setImportLoading] = useState(false)

  // 从URL获取参数
  useEffect(() => {
    const query = _history.location.query || {};
    const code = query.code as string;
    const name = query.planName as string;

    if (code) {
      setPlanCode(code);
      loadData(code);
    }

    if (name) {
      setPlanName(name);
    } else {
      setPlanName('党政干部建议调整方案');
    }
  }, []);

  // 加载征求意见数据
  const loadData = useCallback(async (code: string) => {
    setLoading(true);
    try {
      const res = await request(`/api/swzzbappoint/prepareCadres/findPersonAll?planCode=${code}`);
      if (res && res.code === 0 && res.data) {
        const data = res.data;

        // 处理表格数据
        if (data && Array.isArray(data)) {
          const formattedData = data.map((item: any) => ({
            id: item.code || `temp_${Date.now()}_${Math.random()}`,
            unit: item.positionName || '',
            position: item.positionCode || '',
            currentPosition: item.currentPosition || '',
            selectedCandidateCode: item.selectedCandidateCode || '',
            selectedCandidateName: item.selectedCandidateName || '',
            selectedPositionName: item.selectedPositionName || '',
            selectedPositionCode: item.selectedPositionCode || '',
            remarks: item.remarks || '',
            replyLetter: item.replyLetter,
          }));

          setTableData(formattedData);

          // 默认选中第一条数据
          if (formattedData.length > 0) {
            showOpinionForm(formattedData[0], code);
          }
        } else {
          message.warning('暂无数据');
        }
      } else {
        message.error(res?.message || '加载数据失败');
      }
    } catch (error) {
      console.error('加载数据出错:', error);
      message.error('加载数据出错，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, []);

  // 通用导出函数
  const exportData = useCallback(
    async (isSingle = false) => {
      if (!planCode) {
        message.error('方案编码不存在');
        return;
      }

      // 单个导出时检查是否选择了人员
      if (isSingle && !currentPerson) {
        message.error('请先选择人员');
        return;
      }

      // 全部导出时检查是否有数据
      if (!isSingle && tableData.length === 0) {
        message.error('暂无数据可导出');
        return;
      }

      setLoading(true);
      try {
        // 根据导出类型确定要导出的数据
        const codes = isSingle && currentPerson ? [currentPerson.id] : tableData.map(item => item.id);
        const requestData = {
          data: {
            codes: codes,
          },
        };

        const res = await request('/api/swzzbappoint/prepareCadres/exportCommentLetter', {
          method: 'POST',
          body: requestData,
        });

        if (res) {
          fileDownload(`/api${res.data}`);
          message.success(isSingle ? '导出成功' : '全部导出成功');
        } else {
          message.error('导出失败');
        }
      } catch (error) {
        console.error('导出出错:', error);
        message.error('导出出错，请稍后重试');
      } finally {
        setLoading(false);
      }
    },
    [planCode, currentPerson, tableData]
  );

  // 导出单个意见函 - 使用通用导出函数
  const exportOpinionForm = useCallback(() => exportData(true), [exportData]);

  // 全部导出 - 使用通用导出函数
  const exportAll = useCallback(() => exportData(false), [exportData]);

  // 保存报告沟通内容
  const saveReportContent = useCallback(async () => {
    if (!currentPerson || !reportHtmlContent) {
      message.error('无有效数据可保存');
      return;
    }

    setSaveBtnLoading(true);
    try {
      const reportRes = await request('/api/swzzbappoint/examinationMaterials/saveSeekOpinions', {
        method: 'POST',
        body: {
          data: {
            code: currentPerson.id,
            selectedCandidateCode: currentPerson.selectedCandidateCode,
            fileContext: reportHtmlContent,
          },
        },
      });

      if (reportRes && reportRes.code === 0) {
        message.success('保存成功');
        showOpinionForm(currentPerson, planCode);
      } else {
        message.error('保存失败');
      }
    } catch (error) {
      console.error('保存数据出错:', error);
      message.error('保存数据出错，请稍后重试');
    } finally {
      setSaveBtnLoading(false);
    }
  }, [currentPerson, reportHtmlContent]);

  // 点击姓名显示征求意见函
  const showOpinionForm = useCallback(async (record: TableItem, planCode: string) => {
    setCurrentPerson(record);
    setLoading(true);

    try {
      // 调用新接口获取HTML内容
      const reportRes = await request(
        `/api/swzzbappoint/examinationMaterials/findSeekOpinions?${qs.stringify({
          planCode: planCode,
          code: record.id,
          selectedCandidateCode: record.selectedCandidateCode,
        })}`
      );

      if (reportRes && reportRes.code === 0 && reportRes.data) {
        setReportHtmlContent(reportRes.data.fileContext);
      } else {
        setReportHtmlContent('');
        message.warning('获取报告沟通内容失败');
      }
    } catch (error) {
      message.warning('获取征求函信息出错，显示默认数据');
    } finally {
      setLoading(false);
    }
  }, []);
  const importFile = async (file) => {
    console.log(file, 'filefilefilefile')
    const reportRes = await request(
      `/api/swzzbappoint/prepareCadres/uploadReplyLetter?${qs.stringify({
        url: file.url,
        code: currentPerson.id,
        selectedCandidateCode: currentPerson.selectedCandidateCode,
      })}`
    );
    if (reportRes && reportRes.code === 0) {
      message.success('上传成功');
      const query = _history.location.query || {};
      const code = query.code as string;
      const name = query.planName as string;
      loadData(code)
    }
    console.log(reportRes, 'rrrrrrrrrrrrrrrrrrrrr');
  }
  const handleUpload = info => {
    if (info.file.status === 'done') {
      if (info.file.response && info.file.response.code === 0) {
        const file = info.file.response.data[0];

        // 调用导入接口
        importFile(file);
      } else {
        message.error(info.file.response?.message || '文件上传失败');
        setImportLoading(false);
      }
    } else if (info.file.status === 'error') {
      message.error('文件上传失败');
      setImportLoading(false);
    }
  }
  const props = {
    name: 'file',
    action: '/api/base/upload',
    headers: {
      Authorization: sessionStorage.getItem('authorization') || ''
    },
  }
  // 渲染左侧列表
  const renderLeftPanel = useMemo(
    () => (
      <div className={styles.leftPanel}>
        <table className={styles.customTable}>
          <thead>
            <tr>
              <th>单位及岗位</th>
              <th>有关人选</th>
              <th>复函</th>
            </tr>
          </thead>
          <tbody>
            {tableData.map(record => (
              <tr
                key={record.selectedCandidateCode}
                className={currentPerson?.selectedCandidateCode === record.selectedCandidateCode ? styles.activeRow : ''}
                onClick={() => showOpinionForm(record, planCode)}
              >
                <td>{record.currentPosition}</td>
                <td className={styles.nameCell}>{record.selectedCandidateName}</td>
                <td>
                  <Spin spinning={importLoading}>
                    <Upload {...props} onChange={handleUpload} showUploadList={false}>
                      {
                        <Button size={'small'}>
                          附件上传
                        </Button>
                      }
                    </Upload>
                  </Spin>

                  <div style={{
                    // width: '100px',
                    wordWrap: 'break-word',
                    wordBreak: 'break-all',      // 允许在单词内换行
                    whiteSpace: 'normal',
                    overflowWrap: 'break-word',
                    color: '#36d',
                    cursor: 'pointer',
                    margin: 'auto'
                  }} onClick={(e) => {
                    e.stopPropagation()
                    fileDownload(`/api${record?.replyLetter}`, record?.replyLetter?.split('\\').pop())
                  }
                  }>{record?.replyLetter ? record.replyLetter.split('\\').pop() : ''}</div>
                </td>
              </tr>
            ))}
            {tableData.length === 0 && (
              <tr>
                <td colSpan={3}>暂无数据</td>
              </tr>
            )}
          </tbody>
        </table>

        <div className={styles.listFooter}>
          <span>点击姓名显示征求函</span>
        </div>
      </div>
    ),
    [tableData, currentPerson, showOpinionForm]
  );

  // 渲染右侧征求意见函
  const renderRightPanel = useMemo(() => {
    if (!currentPerson || !reportHtmlContent) {
      return null;
    }
    return (
      <div className={styles.rightPanel}>
        <EditWord
          id={`report-communication-${currentPerson.id}`}
          value={reportHtmlContent}
          onChange={content => setReportHtmlContent(content)}
          style={{
            width: '100%',
            height: '100%',
            minHeight: '600px',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            padding: '20px 200px',
          }}
        />
      </div>
    );
  }, [currentPerson, reportHtmlContent]);

  return (
    <Spin spinning={loading}>
      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles.title}>{planName}</div>
          <Space>
            <Button type="primary" onClick={saveReportContent} loading={saveBtnLoading}>
              保存
            </Button>
            <Button type="primary" onClick={exportOpinionForm}>
              导出
            </Button>
            <Button type="primary" onClick={exportAll}>
              全部导出
            </Button>
          </Space>
        </div>

        <div className={styles.contentLayout}>
          {renderLeftPanel}
          {renderRightPanel}
        </div>
      </div>
    </Spin>
  );
};

export default MeetingCommunicate;
