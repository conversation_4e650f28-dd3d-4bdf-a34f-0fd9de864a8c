.tableTitle {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  letter-spacing: 0.3em;
  font-weight: bolder;
  font-family: 'Times New Roman', fzxbs;
  font-size: 33px;

  .desc {
    letter-spacing: 0.1em;
    font-family: 'Times New Roman', fzkt;
    font-size: 22px;
  }
}

.tableInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-family: 'Times New Roman', fzkt;
  font-size: 20px;
}

.tableHeadBox {
  width: 100%;
  position: relative;
  // overflow-y: scroll;
  // overflow-x: hidden;
}

.sftable {
  margin: 0 auto;
  font-family: 'Times New Roman';
  border: 2px solid;

  :global {
    .ant-select-selector {
      border: none !important;
    }

    .ant-select-selection-item {
      text-align: center;
    }

    .ant-select-arrow {
      display: none !important;
    }

    // 悬停显示删除按钮的样式
    .person-item-hover {
      &:hover .delete-btn-hover {
        opacity: 1 !important;
      }
    }

    // 悬停显示删除按钮的样式
    .person-item-hover {
      &:hover .delete-btn-hover {
        opacity: 1 !important;
      }
    }
  }

  thead {
    td {
      height: 60px;
      border-bottom: none !important;
      font-family: 'Times New Roman', fzht;
      font-size: 20px;
      font-weight: bold;
    }
  }

  tbody {
    td {
      border: 1px solid #000;
      height: 50px;

      & > div {
        font-family: 'Times New Roman', fzht;
        font-size: 19px;
      }

      :global {
        .ant-select,
        .ant-select-selector {
          height: 100% !important;
          align-items: center;
          padding: 0 !important;
        }

        .ant-select-selection-item {
          white-space: pre-wrap;
          padding-right: 5px !important;
        }
      }

      & > span:first-child {
        font-family: 'Times New Roman', fzfs;
        font-size: 20px;
        font-weight: bold;
        padding-top: 5px;
        display: inline-block;
      }

      p {
        text-indent: 2em;
        margin: 0;
        font-family: 'Times New Roman', fzkt;
        font-size: 18px;
      }

      textarea {
        border: none !important;
        padding: 2px;
        font-family: 'Times New Roman', fzfs;
        font-size: 18px;
      }
    }
  }

  .action {
    // display: none;
    width: 50px;
    height: 100%;
    padding: 1px 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: -51px;
    top: 0px;
    background-color: #ffffff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .tableRow {
    position: relative;
    height: 150px;

    .hover-icon {
      display: none;
    }

    .person-select:hover .hover-icon {
      display: block;
    }
  }

  // .tableRow:hover .action {
  //   width: 160px;
  //   padding: 10px;
  //   display: flex;
  //   position: absolute;
  //   right: -160px;
  //   top: 10px;
  //   background-color: #ffffff;
  //   border-radius: 4px;
  //   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  // }
}

.note {
  border: 2px dashed #000;
  font-weight: bolder;
  width: 252px;
  padding: 6px;
  font-family: 'Times New Roman', fzkt;
  font-size: 20px;
}

.meetingCommunicateTableRow {
  font-family: 'Times New Roman', fzfs;
  font-size: 20px;
  font-weight: bold;
  padding-top: 5px;
}

.fileAside {
  border-radius: 4px;
  padding: 16px 0;
  background: #fff;
  border: 1px solid #e8e8e8;
  width: 232px;
  height: 700px;

  .fileList {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .fileItem {
    height: 36px;
    line-height: 36px;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
  }

  // 标题项样式
  .headerItem {
    font-weight: 500;
    // background: #f0f7ff;
    padding-left: 24px;
    padding-right: 16px;

    // 小蓝条样式 - 仅标题有
    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      height: 80%;
      width: 3px;
      background: #1890ff;
    }
  }

  // 内容项样式
  .contentItem {
    padding-left: 24px;
    padding-right: 16px;
    font-weight: bold; // 内容区域字体加粗

    &:hover {
      color: #1890ff;
      background: #E4F2FF;
    }
  }

  // 选中项样式
  .selectedItem {
    background: #E4F2FF;
    color: #1890ff; // 选中内容字体变蓝色
  }

  .fileItemContent {
    display: flex;
    align-items: center;
  }

  .fileIcon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
    position: relative;
    top: -5px;
  }

  .fileName {
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.dyContainer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding-right: 100px;

  .dyRight {
    flex: 1;
  }

  .dyLeft {
    padding-top: 40px;
  }
}

// 添加容器样式，用于定位悬浮框
.fileAsideContainer {
  position: relative;
  display: flex;
}

// 左侧文件列表样式
.fileList {
  width: 232px; // 与之前设置的宽度一致
  // 其他样式保持不变
  position: relative;
}

// 悬浮框样式
.floatPanel {
  position: absolute;
  left: 242px; // 左侧框框宽度 + 间距
  top: -17px; // 与左侧框框顶部对齐
  width: 300px;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  padding: 16px;
}

.floatPanelHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
  font-weight: 500;
  position: relative;

  // 小蓝条样式 - 仅标题有
  &:before {
    content: '';
    position: absolute;
    left: -16px;
    top: 0;
    height: 80%;
    width: 3px;
    background: #1890ff;
  }
}

.closeButton {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  color: #999;

  &:hover {
    color: #333;
  }
}

.uploadButtonContainer {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.uploadButton {
  background: #1890ff;
  color: white;
}

.fileListContainer {
  max-height: 300px;
  overflow-y: auto;
}

.floatFileItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.floatFileName {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.fileActions {
  display: flex;
  gap: 8px;
}

.downloadButton,
.deleteButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
}

.actionIcon {
  width: 16px;
  height: 16px;
}
