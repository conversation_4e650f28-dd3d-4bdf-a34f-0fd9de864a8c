import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Space, Button, message, Spin, Upload } from 'antd';
import { _history } from '@/utils/session';
import styles from './index.less';
import request from '@/utils/request';
import { fileDownload } from '@/utils/method';
import moment from 'moment';
import EditWord from '@/components/EditWord';
import qs from 'qs';
// 定义表格项接口
interface TableItem {
  id: string;
  unit: string; // 单位
  position: string; // 岗位
  remarks: string; // 备注
  selectedCandidateName: string; // 有关人选姓名
  selectedCandidateCode: string; // 有关人选编码
}

// 定义意见函据接口
interface OpinionFormData {
  memName: string;
  birthYear: string;
  oldJob: string; // 当前职位
  newJob: string; // 目标职位
  unit?: string; // 单位
  num: string; // 序号
  date?: string; // 日期
  concat?: string; // 联系人
  contactNumber?: string; // 联系电话
  address?: string; // 单位地址
  [key: string]: any; // 其他可能的属性
}

const MeetingCommunicate: React.FC = () => {
  // 状态管理
  const [tableData, setTableData] = useState([{ code: '', unit: '', name: '' }]);
  const [planCode, setPlanCode] = useState<string>('');
  const [planName, setPlanName] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [currentPerson, setCurrentPerson] = useState({ code: '', unit: '', name: '' });
  const [opinionData, setOpinionData] = useState<OpinionFormData | null>(null);
  const [saveBtnLoading, setSaveBtnLoading] = useState<boolean>(false);
  const [loadingOver, setLoadingOver] = useState(false);
  const [materialContent, setMaterialContent] = useState<string>('');
  const [examinationLoading, setExaminationLoading] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const querys = _history.location.query || {};
  const [fileUrl, setFileUrl] = useState(querys?.fileUrl || ''); // 文件下载路径
  const [htmlContent, setHtmlContent]: any = useState(sessionStorage.getItem('archiveMaterialsFile') || ''); // 从sessionStorage获取html内容

  // 从URL获取参数
  useEffect(() => {
    const query = _history.location.query || {};
    const code = query.code as string;
    const name = query.planName as string;

    if (code) {
      setPlanCode(code);
      loadData(code);
    }

    if (name) {
      setPlanName(name);
    } else {
      setPlanName('党政干部建议调整方案');
    }
  }, []);

  // 加载征求意见数据
  const loadData = useCallback(async (code: string) => {
    setLoading(true);
    try {
      const url = `/api/swzzbappoint/prepareCadres/findPersonPublicNotice?planCode=${code}`;
      const res = await request(url);
      if (res && res.code === 0 && res.data) {
        const data = res.data;

        // 处理表格数据
        if (data && Array.isArray(data)) {
          setTableData(data);

          // 默认选中第一条数据
          if (data.length > 0) {
            showOpinionForm(data[0]);
          }
        } else {
          message.warning('暂无数据');
        }
      } else {
        message.error(res?.message || '加载数据失败');
      }
    } catch (error) {
      console.error('加载数据出错:', error);
      message.error('加载数据出错，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, []);

  // 导出征求意见函
  const exportOpinionForm = useCallback(async () => {
    if (!planCode) {
      message.error('方案编码不存在');
      return;
    }

    if (!currentPerson.code) {
      message.error('请先选择人员');
      return;
    }

    setLoading(true);
    try {
      const res = await request(`/api/swzzbappoint/prepareCadres/exportTopicCommunicate?planCode=${planCode}&personId=${currentPerson.code}`, {
        method: 'GET',
        responseType: 'blob',
      });

      if (res) {
        fileDownload(res, `${currentPerson.name || '人选'}_征求意见函.docx`);
        message.success('导出成功');
      } else {
        message.error('导出失败');
      }
    } catch (error) {
      console.error('导出出错:', error);
      message.error('导出出错，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, [planCode, currentPerson]);

  // 全部导出
  const exportAll = useCallback(async () => {
    if (!planCode) {
      message.error('方案编码不存在');
      return;
    }

    setLoading(true);
    try {
      const res = await request(`/api/swzzbappoint/prepareCadres/exportAllTopicCommunicate?planCode=${planCode}`, {
        method: 'GET',
        responseType: 'blob',
      });

      if (res) {
        fileDownload(res, `${planName || '征求意见函-汇总'}.docx`);
        message.success('全部导出成功');
      } else {
        message.error('导出失败');
      }
    } catch (error) {
      console.error('导出出错:', error);
      message.error('导出出错，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, [planCode, planName]);

  // 保存征求意见函数据
  const saveOpinionData = async () => {
    console.log('materialContent', materialContent);
    console.log(currentPerson, 'currentPerson');
    if (!currentPerson) {
      message.error('无有效数据可保存');
      return;
    }

    setSaveBtnLoading(true);
    try {
      const res = await request('/api/swzzbappoint/examinationMaterials/saveExaminationMaterials', {
        method: 'POST',
        body: {
          data: {
            inspectionCode: currentPerson.code,
            type: 12,
            fileContext: materialContent,
          },
        },
      });

      if (res && res.code === 0) {
        message.success('保存成功');
      } else {
        message.error(res?.message || '保存失败');
      }
    } catch (error) {
      console.error('保存数据出错:', error);
      message.error('保存数据出错，请稍后重试');
    } finally {
      setSaveBtnLoading(false);
    }
  };
  // 公示结束
  const handleOver = async () => {
    setLoadingOver(true);
    try {
      const res = await request(`/api/swzzbappoint/recommendInspection/updateStatus?planCode=${planCode}&status=3`);
      if (res && res.code === 0) {
        message.success('公示结束');
      } else {
        message.error(res?.message || '公示结束失败');
      }
    } catch (error) {
      console.error('公示结束出错:', error);
    } finally {
      setLoadingOver(false);
    }
  };
  // 点击姓名显示征求意见函
  const showOpinionForm = useCallback(async record => {
    setCurrentPerson(record);
    // setExaminationLoading(true);
    try {
      const res = await request(
        `/api/swzzbappoint/examinationMaterials/findPublicNotice?${qs.stringify({
          code: record.code,
          planCode: planCode || _history.location.query.code,
        })}`,
        {}
      );
      if (res && res.code === 0) {
        let { fileContext } = res.data;
        setMaterialContent(fileContext);
      } else {
        // // 如果接口调用失败，使用默认数据
        // setOpinionData({
        //   memName: record.selectedPositionName,
        //   birthYear: '',
        //   oldJob: '',
        //   newJob: '',
        //   unit: '',
        //   num: '',
        //   date: '',
        //   concat: '',
        //   contactNumber: '023-63899719，63899794（传真）',
        //   address: '重庆市渝中区中山四路36号',
        // });
        // message.warning('获取征求函详情信息失败，显示默认数据');
      }
    } catch (error) {
      console.error('获取征求函信息出错:', error);
    } finally {
      // setExaminationLoading(false);
    }
  }, []);

  //导出公示文档
  const exportPublicity = async () => {
    console.log(tableData, currentPerson, '22tableData');
    try {
      const response = await request('/api/swzzbappoint/examinationMaterials/exportAll', {
        method: 'POST',
        body: {
          data: {
            inspectionCode: currentPerson.code,
            types: [12],
          },
        },
      });

      if (response && response.code === 0) {
        fileDownload(`/api${response.data}`, `${currentPerson.name}公示文档.doc`);
      } else {
        message.error(response.message || '下载失败');
      }
    } catch (error) {
      console.error('下载失败:', error);
      message.error('下载失败，请重试');
    }
  };

  // 处理可编辑div内容变更
  const handleValueChange = useCallback(
    (field: string, value: string) => {
      if (opinionData) {
        setOpinionData({
          ...opinionData,
          [field]: value,
        });
      }
    },
    [opinionData]
  );

  // 可编辑的div组件
  const EditableDiv = useCallback(
    ({ field, value, prefix = '' }: { field: string; value?: string; prefix?: string }) => (
      <div
        contentEditable
        suppressContentEditableWarning
        className={styles.editableDiv}
        onBlur={e => handleValueChange(field, e.currentTarget.textContent || '')}
        dangerouslySetInnerHTML={{ __html: `${prefix}${value || ''}` }}
      />
    ),
    [handleValueChange]
  );

  // 获取要显示的HTML内容
  const getContentHtml = () => {
    // 直接返回后端提供的HTML字符串
    return materialContent || '';
  };
  // 处理文件上传前的检查
  const beforeUpload = file => {
    // 检查文件大小
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('文件大小不能超过2MB!');
      return Upload.LIST_IGNORE;
    }

    setImportLoading(true);
    return true;
  };
  // 导入文件
  const importFile = async (file) => {
    console.log('导入文件:', file);
    if (file.url) {
      setFileUrl(file.url);
      try {
        const importRes = await request(`/api/swzzbappoint/examinationMaterials/uploadArchiveMaterials?id=${querys?.id}&url=${file.url}`, {
          method: 'GET',
        });

        if (importRes && importRes.code === 0) {
          message.success('导入成功');
        } else {
          message.error(importRes.message || '导入失败');
        }
      } catch (error) {
        console.error('导入失败:', error);
        message.error('导入失败，请重试');
      } finally {
        setImportLoading(false);
      }
    }
  };

  // 导入文件
  const importFileRow = async (file, record?) => {
    console.log('导入文件:', file);
    if (file.url) {
      setFileUrl(file.url);
      try {
        const importRes = await request(`/api/swzzbappoint/prepareCadres/publicNoticeUpload?code=${record.code}&url=${file.url}`, {
          method: 'GET',
        });

        if (importRes && importRes.code === 0) {
          message.success('导入成功');
          loadData(planCode)
        } else {
          message.error(importRes.message || '导入失败');
        }
      } catch (error) {
        console.error('导入失败:', error);
        message.error('导入失败，请重试');
      } finally {
        setImportLoading(false);
      }
    }
  };

  // 处理文件上传变更
  const handleUploadChange = info => {
    if (info.file.status === 'done') {
      if (info.file.response && info.file.response.code === 0) {
        const file = info.file.response.data[0];

        // 调用导入接口
        importFile(file);
      } else {
        message.error(info.file.response?.message || '文件上传失败');
        setImportLoading(false);
      }
    } else if (info.file.status === 'error') {
      message.error('文件上传失败');
      setImportLoading(false);
    }
  };
  // 导出文件
  const doDownload = () => {
    if (fileUrl) {
      fileDownload(`/api${fileUrl}`);
    }
  };

  const handleUpload = (info, record) => {
    if (info.file.status === 'done') {
      if (info.file.response && info.file.response.code === 0) {
        const file = info.file.response.data[0];

        // 调用导入接口
        importFileRow(file, record);
      } else {
        message.error(info.file.response?.message || '文件上传失败');
        setImportLoading(false);
      }
    } else if (info.file.status === 'error') {
      message.error('文件上传失败');
      setImportLoading(false);
    }
  }

  // 渲染左侧列表
  const renderLeftPanel = useMemo(
    () => {
      const props = {
        name: 'file',
        action: '/api/base/upload',
        headers: {
          Authorization: sessionStorage.getItem('authorization') || ''
        },
      }
      return (
        <div className={styles.leftPanel}>
          <table className={styles.customTable}>
            <thead>
              <tr>
                <th>单位</th>
                <th>有关人选</th>
                <th>复函</th>
              </tr>
            </thead>
            <tbody>
              {tableData.map(record => (
                <tr key={record.code} className={currentPerson?.code === record.code ? styles.activeRow : ''} onClick={() => showOpinionForm(record)}>
                  <td>{record.unit}</td>
                  <td className={styles.nameCell}>
                    {record?.name.length > 0 &&
                      record?.name.map(val => {
                        return (
                          <span key={val} style={{ display: 'block' }}>
                            {val}
                          </span>
                        );
                      })}
                  </td>
                  <td>
                    <Spin spinning={importLoading}>
                      <Upload {...props} onChange={(info) => handleUpload(info, record)} showUploadList={false}>
                        {
                          <Button size={'small'}>
                            附件上传
                          </Button>
                        }
                      </Upload>
                    </Spin>

                    <div style={{
                      // width: '100',
                      wordWrap: 'break-word',
                      wordBreak: 'break-all',      // 允许在单词内换行
                      whiteSpace: 'normal',
                      overflowWrap: 'break-word',
                      color: '#36d',
                      cursor: 'pointer',
                      margin: 'auto'
                    }} onClick={(e) => {
                      e.stopPropagation()
                      fileDownload(`/api${record?.fileUrl}`, record?.fileUrl?.split('\\').pop())
                    }
                    }>{record?.fileUrl ? record.fileUrl.split('\\').pop() : ''}</div>
                  </td>
                </tr>
              ))}
              {tableData.length === 0 && (
                <tr>
                  <td colSpan={3}>暂无数据</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )
    },
    [tableData, currentPerson, showOpinionForm]
  );
  // 渲染右侧征求意见函
  const renderRightPanel = useMemo(() => {
    return (
      <div className={styles.materialContent}>
        <div className={styles.editableContent}>
          <Spin spinning={examinationLoading} tip="加载中...">
            <EditWord
              id={`edit-${currentPerson?.code}`}
              value={getContentHtml()}
              onChange={content => {
                setMaterialContent(content);
              }}
              style={{ width: '100%', height: '100%', margin: '0 auto', padding: '20px 10px', border: '1px solid #d9d9d9', borderRadius: '2px', overflow: 'auto', position: 'relative' }}
            />
          </Spin>
        </div>
      </div>
    );
  }, [currentPerson, materialContent]);

  return (
    <Spin spinning={loading}>
      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles.title}>{planName}</div>
        </div>
        {querys?.comprehensive !== 'true' && (
          <div className={styles.topButtons}>
            <Button type="primary" onClick={saveOpinionData}>
              保存
            </Button>
            <Button type="primary" onClick={exportPublicity}>
              导出公示文档
            </Button>
            {/* <Button
            type="primary"
            loading={loadingOver}
            onClick={() => {
              handleOver();
            }}
          >
            公示结束
          </Button> */}
            <Button type="primary">生成公示转递单</Button>
          </div>
        )}
        {querys?.comprehensive === 'true' && (
          <div className={styles.topButtons}>
            <Upload
              headers={{ Authorization: sessionStorage.getItem('authorization') || '' }}
              action="/api/base/upload"
              beforeUpload={beforeUpload}
              onChange={handleUploadChange}
              showUploadList={false}
              accept=".docx,.doc"
            >
              <Button type="primary" loading={importLoading}>
                导入文件
              </Button>
            </Upload>
            <Button type="primary" onClick={fileUrl ? doDownload : () => { }}>
              导出文件
            </Button>
          </div>
        )}
        {/* 如果导入文件，页面将只显示导入文件的内容，不再显示原来的内容 */}
        {htmlContent ? (
          <div dangerouslySetInnerHTML={{ __html: htmlContent }} />
        ) : (
          <div className={styles.contentLayout}>
            {renderLeftPanel}
            {renderRightPanel}
          </div>
        )}
      </div>
    </Spin>
  );
};

export default MeetingCommunicate;
