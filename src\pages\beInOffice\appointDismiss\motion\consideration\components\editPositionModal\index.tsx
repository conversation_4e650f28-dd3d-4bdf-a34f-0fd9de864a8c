import React, { useState, useEffect } from 'react';
import { Button, Input, Modal, Upload, Form, message } from 'antd';
import { fileDownload } from '@/utils/method';

const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
    },
};
interface editPositionModalProps {
    visible: boolean;
    onCancel: () => void;
    onConfirm: (values) => void;
    chooseType: string;
    data: any;
}



const editPositionModal = (props: editPositionModalProps) => {
    const [form] = Form.useForm();

    const [loading, setLoading] = useState(false);
    const [importLoading, setImportLoading] = useState(false);
    const [file, setFile] = useState('');
    const { chooseType, onCancel, onConfirm, visible, data } = props
    const handleOnCancel = () => {
        onCancel();
    };
    const handleSubmit = async () => {
        try {
            setLoading(true);
            const values = await form.validateFields();
            onConfirm && onConfirm({
                ...values,
                fileUrl: file
            })
            setLoading(false);
        } catch (error) {

        }
    };
    const handleUploadChange = async (info) => {
        console.log(info);
        const fileUrl = await handleUploadleaderNotesChange(info)
        setFile(fileUrl)
    }
    // 处理文件上传变更
    const handleUploadleaderNotesChange = async (info, text?) => {
        if (info.file.status === 'done') {
            if (info.file.response && info.file.response.code === 0) {
                const file = info.file.response.data[0];
                return file.url;
            } else {
                message.error(info.file.response?.message || '文件上传失败');
                setImportLoading(false);
            }
        } else if (info.file.status === 'error') {
            message.error('文件上传失败');
            setImportLoading(false);
        }
    };
    useEffect(() => {
        console.log(data, 'dataqqqqeditPositionModaleditPositionModal');
        if (data) {
            form.setFieldsValue({
                positionName: data?.positionName,
                jobDescription: data?.jobExtend1?.jobDescription,
            })
            setFile(data?.jobExtend1.fileUrl)
        }
    }, [visible])
    return (
        <Modal
            title={"岗位修改"}
            visible={visible}
            onCancel={handleOnCancel}
            footer={[
                <Button key="cancel" onClick={handleOnCancel}>
                    取消
                </Button>,
                <Button key="submit" type="primary" loading={loading} onClick={handleSubmit}>
                    确定
                </Button>,
            ]}
            width={700}
            maskClosable={false}
            destroyOnClose
        >
            {(() => {
                return <Form
                    {...formItemLayout}
                    form={form}
                >

                    <Form.Item name="positionName" label="岗位" rules={[{ required: true, message: '请输入岗位' }]}>
                        <Input placeholder="请输入岗位" />
                    </Form.Item>

                    {chooseType != '2' && <Form.Item name="jobDescription" label="岗位说明">
                        <Input.TextArea placeholder="请输入岗位说明" rows={3} />
                    </Form.Item>}
                    <Form.Item name="fileUrl" label="文件上传">
                        <Upload
                            headers={{ Authorization: sessionStorage.getItem('authorization') || '' }}
                            action="/api/base/upload"
                            showUploadList={false}
                            multiple={false}
                            onChange={handleUploadChange}
                            accept=".docx,.doc.wps"
                        >
                            <Button loading={importLoading}>
                                导入文件
                            </Button>
                        </Upload>
                    </Form.Item>
                    <div style={{
                        // width: '100%',
                        wordWrap: 'break-word',
                        wordBreak: 'break-all',      // 允许在单词内换行
                        whiteSpace: 'normal',
                        overflowWrap: 'break-word',
                        color: '#36d',
                        cursor: 'pointer',
                        marginLeft: "165px"
                    }} onClick={(e) => {
                        e.stopPropagation()
                        fileDownload(`/api${file}`, file?.split('\\').pop())
                    }
                    }>{file ? file.split('\\').pop() : ''}</div>
                </Form>
            })()}
        </Modal>
    );
}

export default editPositionModal;
